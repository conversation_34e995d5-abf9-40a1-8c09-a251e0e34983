
import React from 'react';
import Header from '../components/Header';
import SourcePanel from '../components/SourcePanel';
import ControlPanel from '../components/ControlPanel';
import DestinationPanel from '../components/DestinationPanel';
import { QueryProvider } from '../context/QueryContext';

const Index = () => {
  return (
    <QueryProvider>
      <div className="h-screen bg-gradient-to-br from-slate-100 via-purple-50 to-blue-100 flex flex-col overflow-hidden">
        <Header />
        {/* Desktop Layout */}
        <div className="hidden lg:flex flex-1 flex-row p-2 gap-2 min-h-0">
          <div className="w-2/5 min-h-0">
            <SourcePanel />
          </div>
          <div className="w-1/5 min-h-0">
            <ControlPanel />
          </div>
          <div className="w-2/5 min-h-0">
            <DestinationPanel />
          </div>
        </div>

        {/* Tablet and Mobile Layout */}
        <div className="flex lg:hidden flex-1 flex-col p-2 gap-2 min-h-0 overflow-auto">
          <div className="w-full min-h-0">
            <SourcePanel />
          </div>
          <div className="w-full min-h-0">
            <DestinationPanel />
          </div>
          <div className="w-full min-h-0">
            <ControlPanel />
          </div>
        </div>
      </div>
    </QueryProvider>
  );
};

export default Index;
