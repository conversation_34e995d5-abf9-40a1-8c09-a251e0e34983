
import React from 'react';
import SourcePanel from '../components/SourcePanel';
import ControlPanel from '../components/ControlPanel';
import DestinationPanel from '../components/DestinationPanel';
import { QueryProvider } from '../context/QueryContext';

const Index = () => {
  return (
    <QueryProvider>
      <div className="h-screen bg-gradient-to-br from-slate-100 via-purple-50 to-blue-100 flex flex-col overflow-hidden">
        {/* Desktop Layout */}
        <div className="hidden lg:flex flex-1 flex-row p-2 min-h-0 relative">
          {/* Source Section */}
          <div className="w-2/5 min-h-0 relative z-10">
            <SourcePanel />
          </div>

          {/* Gradient Separator 1 - Source to Control */}
          <div className="w-2 min-h-0 flex items-center justify-center relative">
            <div className="h-full w-full bg-gradient-to-b from-blue-400 via-purple-500 to-indigo-600 rounded-full shadow-lg shadow-purple-500/30 opacity-80"></div>
            <div className="absolute inset-0 bg-gradient-to-b from-blue-300 via-purple-400 to-indigo-500 rounded-full blur-sm opacity-60"></div>
          </div>

          {/* Control Section */}
          <div className="w-1/5 min-h-0 relative z-10 mx-2">
            <ControlPanel />
          </div>

          {/* Gradient Separator 2 - Control to Destination */}
          <div className="w-2 min-h-0 flex items-center justify-center relative">
            <div className="h-full w-full bg-gradient-to-b from-purple-500 via-pink-500 to-green-600 rounded-full shadow-lg shadow-pink-500/30 opacity-80"></div>
            <div className="absolute inset-0 bg-gradient-to-b from-purple-400 via-pink-400 to-green-500 rounded-full blur-sm opacity-60"></div>
          </div>

          {/* Destination Section */}
          <div className="w-2/5 min-h-0 relative z-10">
            <DestinationPanel />
          </div>
        </div>

        {/* Tablet and Mobile Layout */}
        <div className="flex lg:hidden flex-1 flex-col p-2 min-h-0 overflow-auto relative">
          {/* Source Section */}
          <div className="w-full min-h-0 relative z-10">
            <SourcePanel />
          </div>

          {/* Horizontal Gradient Separator 1 - Source to Destination */}
          <div className="h-2 w-full flex items-center justify-center relative my-2">
            <div className="w-full h-full bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 rounded-full shadow-lg shadow-purple-500/30 opacity-80"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-blue-300 via-purple-400 to-pink-400 rounded-full blur-sm opacity-60"></div>
          </div>

          {/* Destination Section */}
          <div className="w-full min-h-0 relative z-10">
            <DestinationPanel />
          </div>

          {/* Horizontal Gradient Separator 2 - Destination to Control */}
          <div className="h-2 w-full flex items-center justify-center relative my-2">
            <div className="w-full h-full bg-gradient-to-r from-green-400 via-purple-500 to-indigo-600 rounded-full shadow-lg shadow-purple-500/30 opacity-80"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-green-300 via-purple-400 to-indigo-500 rounded-full blur-sm opacity-60"></div>
          </div>

          {/* Control Section */}
          <div className="w-full min-h-0 relative z-10">
            <ControlPanel />
          </div>
        </div>
      </div>
    </QueryProvider>
  );
};

export default Index;
