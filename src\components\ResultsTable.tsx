
import React, { useState, useMemo } from 'react';
import { Loader2, FileText, ChevronLeft, ChevronRight } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { useQuery } from '../context/QueryContext';

interface ResultsTableProps {
  results: {
    data: Record<string, any>[];
    totalRows: number;
    displayedRows?: number;
    isLimited?: boolean;
  } | null;
  loading: boolean;
  comparison: 'none' | 'completed';
  type: 'source' | 'destination';
}

const ResultsTable = ({ results, loading, comparison, type }: ResultsTableProps) => {
  const { sourceResults, destinationResults } = useQuery();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 1000; // Show 1000 rows per page for better performance

  // Memoized pagination calculations
  const paginationData = useMemo(() => {
    if (!results?.data) return { paginatedData: [], totalPages: 0, startRow: 0, endRow: 0 };

    const totalPages = Math.ceil(results.data.length / rowsPerPage);
    const startIndex = (currentPage - 1) * rowsPerPage;
    const endIndex = Math.min(startIndex + rowsPerPage, results.data.length);
    const paginatedData = results.data.slice(startIndex, endIndex);

    return {
      paginatedData,
      totalPages,
      startRow: startIndex + 1,
      endRow: endIndex
    };
  }, [results?.data, currentPage, rowsPerPage]);

  const getComparisonClass = (globalRowIndex: number) => {
    if (comparison !== 'completed' || !sourceResults || !destinationResults) {
      return '';
    }

    const sourceRow = sourceResults.data[globalRowIndex];
    const destinationRow = destinationResults.data[globalRowIndex];

    if (!sourceRow || !destinationRow) {
      return 'bg-red-50 border-red-200';
    }

    const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
    return isMatch ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
  };

  // Reset to first page when results change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [results]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-sm text-slate-600">Executing query...</p>
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileText className="w-12 h-12 mx-auto mb-4 text-slate-400" />
          <p className="text-sm text-slate-600">No results to display</p>
          <p className="text-xs text-slate-400 mt-1">Execute a query to see results</p>
        </div>
      </div>
    );
  }

  const columns = results.data.length > 0 ? Object.keys(results.data[0]) : [];

  return (
    <div className="space-y-4">
      {/* Results Summary */}
      <div className="bg-slate-50 rounded-lg p-4 border border-slate-200">
        <div className="flex items-center justify-between flex-wrap gap-2">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-sm font-medium text-slate-700">
                {results.isLimited ? 'Displayed' : 'Total'} Rows: {results.data.length.toLocaleString()}
              </span>
            </div>
            {results.isLimited && (
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                <span className="text-sm font-medium text-orange-700">
                  Limited (Total: {results.totalRows.toLocaleString()})
                </span>
              </div>
            )}
          </div>
          {paginationData.totalPages > 1 && (
            <div className="text-sm text-slate-600">
              Showing {paginationData.startRow.toLocaleString()} - {paginationData.endRow.toLocaleString()} of {results.data.length.toLocaleString()}
            </div>
          )}
        </div>
        {results.isLimited && (
          <div className="mt-2 text-xs text-orange-600 bg-orange-50 p-2 rounded border border-orange-200">
            ⚠️ Results limited to {results.data.length.toLocaleString()} rows for performance. Total rows in result set: {results.totalRows.toLocaleString()}
          </div>
        )}
      </div>

      {/* Pagination Controls - Top */}
      {paginationData.totalPages > 1 && (
        <div className="flex items-center justify-between bg-slate-50 rounded-lg p-3 border border-slate-200">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
            >
              First
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="w-4 h-4" />
              Previous
            </Button>
          </div>

          <div className="text-sm font-medium text-slate-700">
            Page {currentPage} of {paginationData.totalPages}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(paginationData.totalPages, prev + 1))}
              disabled={currentPage === paginationData.totalPages}
            >
              Next
              <ChevronRight className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(paginationData.totalPages)}
              disabled={currentPage === paginationData.totalPages}
            >
              Last
            </Button>
          </div>
        </div>
      )}

      {/* Results Table */}
      <div className="border border-slate-200 rounded-lg overflow-hidden">
        <div className="scrollbar-always-visible force-horizontal-scrollbar overflow-y-auto max-h-[600px] min-h-[400px]">
          <Table className="min-w-full table-fixed">
            <TableHeader className="sticky top-0 z-10 bg-slate-100">
              <TableRow className="border-b-2 border-slate-300">
                {columns.map((column, index) => (
                  <TableHead
                    key={column}
                    className="font-semibold text-slate-800 border-r border-slate-200 last:border-r-0 px-4 py-3 text-left table-cell-no-wrap bg-slate-100"
                    style={{
                      minWidth: '150px',
                      width: index === 0 ? '200px' : '150px' // First column slightly wider
                    }}
                  >
                    <div className="truncate" title={column}>
                      {column}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginationData.paginatedData.map((row, index) => {
                const globalIndex = (currentPage - 1) * rowsPerPage + index;
                return (
                  <TableRow
                    key={globalIndex}
                    className={`hover:bg-slate-50 transition-colors border-b border-slate-100 ${getComparisonClass(globalIndex)}`}
                  >
                    {columns.map((column, colIndex) => {
                      const cellValue = row[column];
                      const displayValue = cellValue !== null && cellValue !== undefined ? String(cellValue) : '';

                      return (
                        <TableCell
                          key={column}
                          className="border-r border-slate-100 last:border-r-0 text-sm px-4 py-2 table-cell-no-wrap"
                          style={{
                            minWidth: '150px',
                            width: colIndex === 0 ? '200px' : '150px',
                            maxWidth: colIndex === 0 ? '200px' : '150px'
                          }}
                          title={displayValue} // Show full value on hover
                        >
                          <div className="truncate">
                            {displayValue}
                          </div>
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination Controls - Bottom */}
      {paginationData.totalPages > 1 && (
        <div className="flex items-center justify-between bg-slate-50 rounded-lg p-3 border border-slate-200">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
            >
              First
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="w-4 h-4" />
              Previous
            </Button>
          </div>

          <div className="text-sm font-medium text-slate-700">
            Page {currentPage} of {paginationData.totalPages}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(paginationData.totalPages, prev + 1))}
              disabled={currentPage === paginationData.totalPages}
            >
              Next
              <ChevronRight className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(paginationData.totalPages)}
              disabled={currentPage === paginationData.totalPages}
            >
              Last
            </Button>
          </div>
        </div>
      )}

      {/* Comparison Legend */}
      {comparison === 'completed' && (
        <div className="bg-slate-50 rounded-lg p-4 border border-slate-200">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-slate-700">Matched Rows</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-slate-700">Mismatched Rows</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultsTable;
