
import React, { useState, useMemo, useCallback, useRef } from 'react';
import { Loader2, FileText, ChevronLeft, ChevronRight } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { useQuery } from '../context/QueryContext';

interface ResultsTableProps {
  results: {
    data: Record<string, any>[];
    totalRows: number;
    displayedRows?: number;
    isLimited?: boolean;
  } | null;
  loading: boolean;
  comparison: 'none' | 'completed';
  type: 'source' | 'destination';
}

const ResultsTable = ({ results, loading, comparison, type }: ResultsTableProps) => {
  const { sourceResults, destinationResults } = useQuery();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 1000; // Show 1000 rows per page for better performance

  // Column width state
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});
  const [isResizing, setIsResizing] = useState<string | null>(null);
  const tableRef = useRef<HTMLDivElement>(null);
  const resizeStartX = useRef<number>(0);
  const resizeStartWidth = useRef<number>(0);

  // Memoized pagination calculations
  const paginationData = useMemo(() => {
    if (!results?.data) return { paginatedData: [], totalPages: 0, startRow: 0, endRow: 0 };

    const totalPages = Math.ceil(results.data.length / rowsPerPage);
    const startIndex = (currentPage - 1) * rowsPerPage;
    const endIndex = Math.min(startIndex + rowsPerPage, results.data.length);
    const paginatedData = results.data.slice(startIndex, endIndex);

    return {
      paginatedData,
      totalPages,
      startRow: startIndex + 1,
      endRow: endIndex
    };
  }, [results?.data, currentPage, rowsPerPage]);

  // Memoized comparison statistics
  const comparisonStats = useMemo(() => {
    if (comparison !== 'completed' || !sourceResults || !destinationResults) {
      return { matchedRows: 0, unmatchedRows: 0 };
    }

    let matchedRows = 0;
    let unmatchedRows = 0;
    const maxRows = Math.max(sourceResults.data.length, destinationResults.data.length);

    for (let i = 0; i < maxRows; i++) {
      const sourceRow = sourceResults.data[i];
      const destinationRow = destinationResults.data[i];

      if (!sourceRow || !destinationRow) {
        unmatchedRows++;
      } else {
        const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
        if (isMatch) {
          matchedRows++;
        } else {
          unmatchedRows++;
        }
      }
    }

    return { matchedRows, unmatchedRows };
  }, [comparison, sourceResults, destinationResults]);

  const getComparisonClass = (globalRowIndex: number) => {
    if (comparison !== 'completed' || !sourceResults || !destinationResults) {
      return '';
    }

    const sourceRow = sourceResults.data[globalRowIndex];
    const destinationRow = destinationResults.data[globalRowIndex];

    if (!sourceRow || !destinationRow) {
      return 'bg-red-50 border-red-200';
    }

    const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
    return isMatch ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
  };

  // Column resizing handlers
  const handleMouseDown = useCallback((e: React.MouseEvent, columnName: string) => {
    e.preventDefault();
    setIsResizing(columnName);
    resizeStartX.current = e.clientX;
    resizeStartWidth.current = columnWidths[columnName] || 150;
  }, [columnWidths]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing) return;

    const deltaX = e.clientX - resizeStartX.current;
    const newWidth = Math.max(80, resizeStartWidth.current + deltaX); // Minimum width of 80px

    setColumnWidths(prev => ({
      ...prev,
      [isResizing]: newWidth
    }));
  }, [isResizing]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(null);
  }, []);

  // Add global mouse event listeners for column resizing
  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  // Reset to first page when results change
  React.useEffect(() => {
    setCurrentPage(1);
    setColumnWidths({}); // Reset column widths when results change
  }, [results]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-sm text-slate-600">Executing query...</p>
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileText className="w-12 h-12 mx-auto mb-4 text-slate-400" />
          <p className="text-sm text-slate-600">No results to display</p>
          <p className="text-xs text-slate-400 mt-1">Execute a query to see results</p>
        </div>
      </div>
    );
  }

  const columns = results.data.length > 0 ? Object.keys(results.data[0]) : [];

  return (
    <div className="space-y-4">
      {/* Results Summary - Sticky Header */}
      <div className="sticky top-0 z-20 bg-white border-b border-slate-200 pb-4">
        <div className="bg-slate-50 rounded-lg p-4 border border-slate-200">
          <div className="flex items-center justify-between flex-wrap gap-2">
            <div className="flex items-center gap-4 flex-wrap">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-medium text-slate-700">
                  {results.isLimited ? 'Displayed' : 'Total'} Rows: {results.data.length.toLocaleString()}
                </span>
              </div>

              {comparison === 'completed' && (
                <>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium text-green-700">
                      Matched: {comparisonStats.matchedRows.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium text-red-700">
                      Unmatched: {comparisonStats.unmatchedRows.toLocaleString()}
                    </span>
                  </div>
                </>
              )}

              {results.isLimited && (
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span className="text-sm font-medium text-orange-700">
                    Limited (Total: {results.totalRows.toLocaleString()})
                  </span>
                </div>
              )}
            </div>
            {paginationData.totalPages > 1 && (
              <div className="text-sm text-slate-600">
                Showing {paginationData.startRow.toLocaleString()} - {paginationData.endRow.toLocaleString()} of {results.data.length.toLocaleString()}
              </div>
            )}
          </div>
          {results.isLimited && (
            <div className="mt-2 text-xs text-orange-600 bg-orange-50 p-2 rounded border border-orange-200">
              ⚠️ Results limited to {results.data.length.toLocaleString()} rows for performance. Total rows in result set: {results.totalRows.toLocaleString()}
            </div>
          )}
        </div>
      </div>

      {/* Pagination Controls - Top (Sticky) */}
      {paginationData.totalPages > 1 && (
        <div className="sticky top-[120px] z-10 bg-white border-b border-slate-200 pb-2">
          <div className="flex items-center justify-between bg-slate-50 rounded-lg p-3 border border-slate-200">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
              >
                First
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="w-4 h-4" />
                Previous
              </Button>
            </div>

            <div className="text-sm font-medium text-slate-700">
              Page {currentPage} of {paginationData.totalPages}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(paginationData.totalPages, prev + 1))}
                disabled={currentPage === paginationData.totalPages}
              >
                Next
                <ChevronRight className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(paginationData.totalPages)}
                disabled={currentPage === paginationData.totalPages}
              >
                Last
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Results Table */}
      <div className="border border-slate-200 rounded-lg overflow-hidden">
        <div
          ref={tableRef}
          className="scrollbar-always-visible force-horizontal-scrollbar overflow-auto max-h-[500px] min-h-[400px] relative"
        >
          <Table className="min-w-full">
            <TableHeader className="sticky top-0 z-10 bg-slate-100 border-b-2 border-slate-300">
              <TableRow>
                {columns.map((column, index) => {
                  const columnWidth = columnWidths[column] || (index === 0 ? 200 : 150);
                  return (
                    <TableHead
                      key={column}
                      className="font-semibold text-slate-800 border-r border-slate-200 last:border-r-0 px-4 py-3 text-left table-cell-no-wrap bg-slate-100 relative group"
                      style={{
                        width: columnWidth,
                        minWidth: columnWidth,
                        maxWidth: columnWidth
                      }}
                    >
                      <div className="truncate pr-4" title={column}>
                        {column}
                      </div>
                      {/* Column Resize Handle */}
                      <div
                        className="absolute right-0 top-0 bottom-0 w-1 cursor-col-resize bg-transparent hover:bg-blue-400 group-hover:bg-blue-300 transition-colors"
                        onMouseDown={(e) => handleMouseDown(e, column)}
                        style={{
                          right: '-2px',
                          width: '4px',
                          zIndex: 20
                        }}
                      />
                    </TableHead>
                  );
                })}
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginationData.paginatedData.map((row, index) => {
                const globalIndex = (currentPage - 1) * rowsPerPage + index;
                return (
                  <TableRow
                    key={globalIndex}
                    className={`hover:bg-slate-50 transition-colors border-b border-slate-100 ${getComparisonClass(globalIndex)}`}
                  >
                    {columns.map((column, colIndex) => {
                      const cellValue = row[column];
                      const displayValue = cellValue !== null && cellValue !== undefined ? String(cellValue) : '';
                      const columnWidth = columnWidths[column] || (colIndex === 0 ? 200 : 150);

                      return (
                        <TableCell
                          key={column}
                          className="border-r border-slate-100 last:border-r-0 text-sm px-4 py-2 table-cell-no-wrap"
                          style={{
                            width: columnWidth,
                            minWidth: columnWidth,
                            maxWidth: columnWidth
                          }}
                          title={displayValue} // Show full value on hover
                        >
                          <div className="truncate">
                            {displayValue}
                          </div>
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>

        {/* Sticky Horizontal Scrollbar */}
        <div className="sticky bottom-0 bg-white border-t border-slate-200 overflow-x-auto scrollbar-always-visible">
          <div style={{ width: `${columns.reduce((total, col, index) => total + (columnWidths[col] || (index === 0 ? 200 : 150)), 0)}px`, height: '1px' }}></div>
        </div>
      </div>

      {/* Pagination Controls - Bottom */}
      {paginationData.totalPages > 1 && (
        <div className="flex items-center justify-between bg-slate-50 rounded-lg p-3 border border-slate-200">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
            >
              First
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="w-4 h-4" />
              Previous
            </Button>
          </div>

          <div className="text-sm font-medium text-slate-700">
            Page {currentPage} of {paginationData.totalPages}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(paginationData.totalPages, prev + 1))}
              disabled={currentPage === paginationData.totalPages}
            >
              Next
              <ChevronRight className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(paginationData.totalPages)}
              disabled={currentPage === paginationData.totalPages}
            >
              Last
            </Button>
          </div>
        </div>
      )}

      {/* Comparison Legend */}
      {comparison === 'completed' && (
        <div className="bg-slate-50 rounded-lg p-4 border border-slate-200">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-slate-700">Matched Rows</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-slate-700">Mismatched Rows</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultsTable;
