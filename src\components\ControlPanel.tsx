
import React from 'react';
import { Loader2, Trash2, Play, GitCompare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useQuery } from '../context/QueryContext';
import { useToast } from '@/hooks/use-toast';
import { databaseService } from '../services/databaseService';

const ControlPanel = () => {
  const {
    sourceServer,
    sourceDatabase,
    sourceQuery,
    destinationServer,
    destinationDatabase,
    destinationQuery,
    setSourceResults,
    setDestinationResults,
    isSourceLoading,
    setIsSourceLoading,
    isDestinationLoading,
    setIsDestinationLoading,
    sourceResults,
    destinationResults,
    comparison,
    setComparison,
    setSourceQuery,
    setDestinationQuery,
    isSourceConnected,
    isDestinationConnected,
    setIsSourceAccordionCollapsed,
    setIsDestinationAccordionCollapsed,
  } = useQuery();

  const { toast } = useToast();

  const executeSourceQuery = async () => {
    if (!isSourceConnected || !sourceServer || !sourceDatabase || !sourceQuery.trim()) {
      toast({
        title: "Missing Information",
        description: "Please connect to server, select database, and enter a query for source.",
        variant: "destructive",
      });
      return;
    }

    setIsSourceLoading(true);

    try {
      const results = await databaseService.executeQuery(sourceDatabase, sourceQuery);
      setSourceResults(results);
      setComparison('none');
      setIsSourceAccordionCollapsed(true); // Hide only Source Activities section after execution

      toast({
        title: "Source Query Executed",
        description: `Retrieved ${results.totalRows} rows from ${sourceServer}/${sourceDatabase}. Source section collapsed for better view.`,
      });
    } catch (error) {
      toast({
        title: "Query Execution Failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    } finally {
      setIsSourceLoading(false);
    }
  };

  const executeDestinationQuery = async () => {
    if (!isDestinationConnected || !destinationServer || !destinationDatabase || !destinationQuery.trim()) {
      toast({
        title: "Missing Information",
        description: "Please connect to server, select database, and enter a query for destination.",
        variant: "destructive",
      });
      return;
    }

    setIsDestinationLoading(true);

    try {
      const results = await databaseService.executeQuery(destinationDatabase, destinationQuery);
      setDestinationResults(results);
      setComparison('none');
      setIsDestinationAccordionCollapsed(true); // Hide only Destination Activities section after execution

      toast({
        title: "Destination Query Executed",
        description: `Retrieved ${results.totalRows} rows from ${destinationServer}/${destinationDatabase}. Destination section collapsed for better view.`,
      });
    } catch (error) {
      toast({
        title: "Query Execution Failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    } finally {
      setIsDestinationLoading(false);
    }
  };

  const compareResults = () => {
    if (!sourceResults || !destinationResults) {
      toast({
        title: "Cannot Compare",
        description: "Please execute both source and destination queries first.",
        variant: "destructive",
      });
      return;
    }

    setComparison('completed');
    setIsSourceAccordionCollapsed(true);
    setIsDestinationAccordionCollapsed(true);

    toast({
      title: "Comparison Completed",
      description: "Results have been compared and highlighted. Both sections collapsed for better view.",
    });
  };

  const clearQueries = () => {
    setSourceQuery('');
    setDestinationQuery('');
    setSourceResults(null);
    setDestinationResults(null);
    setComparison('none');
    setIsSourceAccordionCollapsed(false);
    setIsDestinationAccordionCollapsed(false);

    toast({
      title: "Queries Cleared",
      description: "All queries and results have been cleared. Both sections restored.",
    });
  };

  return (
    <div className="h-full flex flex-col bg-gradient-to-b from-purple-50 to-pink-50 shadow-lg shadow-black/50 rounded-lg p-3 sm:p-4 lg:p-5 overflow-hidden">
      {/* Main Application Title */}
      <div className="text-center mb-6 sm:mb-8 lg:mb-10 px-2">
        <div className="relative">
          {/* Main title container with gradient box shadow */}
          <div className="relative bg-white/40 backdrop-blur-sm rounded-xl p-4 sm:p-5 lg:p-6 border border-white/50 shadow-[0_10px_40px_-15px_rgba(79,70,229,0.3),0_4px_25px_-5px_rgba(168,85,247,0.2),0_0_0_1px_rgba(236,72,153,0.1)]">
            {/* Logo */}
            <div className="mb-3">
              <div className="text-3xl sm:text-4xl lg:text-5xl">📊</div>
            </div>

            {/* Title - Two Lines with Center Alignment */}
            <h1 className="text-base sm:text-lg lg:text-xl xl:text-2xl font-extrabold text-transparent bg-gradient-to-r from-indigo-800 via-purple-800 to-pink-800 bg-clip-text tracking-wide font-sans leading-relaxed text-center">
              <div>Query Comparison</div>
              <div>Tool</div>
            </h1>

            <p className="text-xs sm:text-sm text-slate-600 mt-2 font-medium tracking-wide">
              Professional Database Query Analysis & Comparison Platform
            </p>

            {/* Decorative elements */}
            <div className="flex justify-center mt-3">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-indigo-400 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-75"></div>
                <div className="w-2 h-2 bg-pink-400 rounded-full animate-pulse delay-150"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Control Buttons */}
      <div className="flex-1 flex flex-col items-center justify-center">
        <div className="space-y-3 sm:space-y-4 lg:space-y-5 w-full max-w-xs">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={executeSourceQuery}
                disabled={isSourceLoading || !isSourceConnected}
                className="w-full h-10 sm:h-12 lg:h-16 bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 hover:from-blue-600 hover:via-blue-700 hover:to-blue-800 text-white font-semibold text-xs sm:text-sm lg:text-base shadow-xl hover:shadow-2xl transition-all duration-300 border-0 rounded-xl transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
              >
                {isSourceLoading ? (
                  <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 animate-spin mr-2" />
                ) : (
                  <Play className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 mr-2" />
                )}
                Execute Source Query
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Execute the SQL query on the source database</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={executeDestinationQuery}
                disabled={isDestinationLoading || !isDestinationConnected}
                className="w-full h-10 sm:h-12 lg:h-16 bg-gradient-to-r from-green-500 via-green-600 to-green-700 hover:from-green-600 hover:via-green-700 hover:to-green-800 text-white font-semibold text-xs sm:text-sm lg:text-base shadow-xl hover:shadow-2xl transition-all duration-300 border-0 rounded-xl transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
              >
                {isDestinationLoading ? (
                  <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 animate-spin mr-2" />
                ) : (
                  <Play className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 mr-2" />
                )}
                Execute Destination Query
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Execute the SQL query on the destination database</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={compareResults}
                disabled={!sourceResults || !destinationResults}
                className="w-full h-10 sm:h-12 lg:h-16 bg-gradient-to-r from-purple-500 via-purple-600 to-pink-600 hover:from-purple-600 hover:via-purple-700 hover:to-pink-700 text-white font-semibold text-xs sm:text-sm lg:text-base shadow-xl hover:shadow-2xl transition-all duration-300 border-0 rounded-xl transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
              >
                <GitCompare className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 mr-2" />
                Compare Results
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Compare the results from both queries and highlight differences</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={clearQueries}
                className="w-full h-10 sm:h-12 lg:h-16 bg-gradient-to-r from-red-500 via-red-600 to-red-700 hover:from-red-600 hover:via-red-700 hover:to-red-800 text-white font-semibold text-xs sm:text-sm lg:text-base shadow-xl hover:shadow-2xl transition-all duration-300 border-0 rounded-xl transform hover:scale-105"
              >
                <Trash2 className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 mr-2" />
                Clear Queries
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Clear all queries and results to start fresh</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        </div>
      </div>

      {/* Status Section */}
      <div className="mt-4 sm:mt-6 lg:mt-8">
        <div className="bg-white/30 backdrop-blur-sm rounded-lg p-3 border border-white/40 shadow-sm">
          <div className="text-center">
            <h3 className="text-xs sm:text-sm font-semibold text-slate-700 mb-2">Application Status</h3>
            <div className="flex items-center justify-center space-x-4 text-xs text-slate-600">
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${isSourceConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span>Source</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${isDestinationConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span>Destination</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${comparison === 'completed' ? 'bg-blue-500' : 'bg-gray-400'}`}></div>
                <span>Comparison</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ControlPanel;
