
import React from 'react';
import { Loader2, Trash2, Play, GitCompare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useQuery } from '../context/QueryContext';
import { useToast } from '@/hooks/use-toast';
import { databaseService } from '../services/databaseService';

const ControlPanel = () => {
  const {
    sourceServer,
    sourceDatabase,
    sourceQuery,
    destinationServer,
    destinationDatabase,
    destinationQuery,
    setSourceResults,
    setDestinationResults,
    isSourceLoading,
    setIsSourceLoading,
    isDestinationLoading,
    setIsDestinationLoading,
    sourceResults,
    destinationResults,
    comparison,
    setComparison,
    setSourceQuery,
    setDestinationQuery,
    isSourceConnected,
    isDestinationConnected,
    setIsSourceAccordionCollapsed,
    setIsDestinationAccordionCollapsed,
  } = useQuery();

  const { toast } = useToast();

  const executeSourceQuery = async () => {
    if (!isSourceConnected || !sourceServer || !sourceDatabase || !sourceQuery.trim()) {
      toast({
        title: "Missing Information",
        description: "Please connect to server, select database, and enter a query for source.",
        variant: "destructive",
      });
      return;
    }

    setIsSourceLoading(true);

    try {
      const results = await databaseService.executeQuery(sourceDatabase, sourceQuery);
      setSourceResults(results);
      setComparison('none');
      setIsSourceAccordionCollapsed(true); // Hide only Source Activities section after execution

      toast({
        title: "Source Query Executed",
        description: `Retrieved ${results.totalRows} rows from ${sourceServer}/${sourceDatabase}. Source section collapsed for better view.`,
      });
    } catch (error) {
      toast({
        title: "Query Execution Failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    } finally {
      setIsSourceLoading(false);
    }
  };

  const executeDestinationQuery = async () => {
    if (!isDestinationConnected || !destinationServer || !destinationDatabase || !destinationQuery.trim()) {
      toast({
        title: "Missing Information",
        description: "Please connect to server, select database, and enter a query for destination.",
        variant: "destructive",
      });
      return;
    }

    setIsDestinationLoading(true);

    try {
      const results = await databaseService.executeQuery(destinationDatabase, destinationQuery);
      setDestinationResults(results);
      setComparison('none');
      setIsDestinationAccordionCollapsed(true); // Hide only Destination Activities section after execution

      toast({
        title: "Destination Query Executed",
        description: `Retrieved ${results.totalRows} rows from ${destinationServer}/${destinationDatabase}. Destination section collapsed for better view.`,
      });
    } catch (error) {
      toast({
        title: "Query Execution Failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    } finally {
      setIsDestinationLoading(false);
    }
  };

  const compareResults = () => {
    if (!sourceResults || !destinationResults) {
      toast({
        title: "Cannot Compare",
        description: "Please execute both source and destination queries first.",
        variant: "destructive",
      });
      return;
    }

    setComparison('completed');
    setIsSourceAccordionCollapsed(true);
    setIsDestinationAccordionCollapsed(true);

    toast({
      title: "Comparison Completed",
      description: "Results have been compared and highlighted. Both sections collapsed for better view.",
    });
  };

  const clearQueries = () => {
    setSourceQuery('');
    setDestinationQuery('');
    setSourceResults(null);
    setDestinationResults(null);
    setComparison('none');
    setIsSourceAccordionCollapsed(false);
    setIsDestinationAccordionCollapsed(false);

    toast({
      title: "Queries Cleared",
      description: "All queries and results have been cleared. Both sections restored.",
    });
  };

  return (
    <div className="h-full flex flex-col bg-gradient-to-b from-purple-50 to-pink-50 shadow-lg shadow-black/50 rounded-lg p-3 sm:p-4 lg:p-5 overflow-hidden">
      {/* Main Application Title - Elevated Design */}
      <div className="text-center mb-6 sm:mb-8 lg:mb-10 px-2">
        <div className="relative transform hover:scale-105 transition-transform duration-300">
          {/* Multiple layered shadows for depth */}
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-200 via-purple-200 to-pink-200 rounded-2xl opacity-40 blur-lg transform translate-y-2"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-300 via-purple-300 to-pink-300 rounded-2xl opacity-30 blur-md transform translate-y-1"></div>

          {/* Main elevated container */}
          <div className="relative bg-gradient-to-br from-white via-white to-slate-50 backdrop-blur-sm rounded-2xl p-5 sm:p-6 lg:p-8 border-2 border-white/80 shadow-2xl shadow-purple-500/20 hover:shadow-purple-500/30 transition-all duration-300">
            {/* Inner glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-50/50 via-purple-50/50 to-pink-50/50 rounded-2xl"></div>

            {/* Content */}
            <div className="relative z-10">
              <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-black text-transparent bg-gradient-to-r from-indigo-700 via-purple-700 to-pink-700 bg-clip-text tracking-wide font-sans leading-tight drop-shadow-sm">
                📊 Query Comparison Tool
              </h1>
              <p className="text-sm sm:text-base text-slate-700 mt-3 font-semibold tracking-wide drop-shadow-sm">
                Professional Database Query Analysis & Comparison Platform
              </p>

              {/* Enhanced decorative elements */}
              <div className="flex justify-center mt-4">
                <div className="flex space-x-2 p-2 bg-white/50 rounded-full shadow-inner">
                  <div className="w-3 h-3 bg-gradient-to-r from-indigo-400 to-indigo-500 rounded-full animate-pulse shadow-lg shadow-indigo-300/50"></div>
                  <div className="w-3 h-3 bg-gradient-to-r from-purple-400 to-purple-500 rounded-full animate-pulse delay-75 shadow-lg shadow-purple-300/50"></div>
                  <div className="w-3 h-3 bg-gradient-to-r from-pink-400 to-pink-500 rounded-full animate-pulse delay-150 shadow-lg shadow-pink-300/50"></div>
                </div>
              </div>

              {/* Bottom accent line */}
              <div className="mt-4 h-1 bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 rounded-full shadow-lg"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Control Buttons */}
      <div className="flex-1 flex flex-col items-center justify-center">
        <div className="space-y-3 sm:space-y-4 lg:space-y-5 w-full max-w-xs">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={executeSourceQuery}
                disabled={isSourceLoading || !isSourceConnected}
                className="w-full h-10 sm:h-12 lg:h-16 bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 hover:from-blue-600 hover:via-blue-700 hover:to-blue-800 text-white font-semibold text-xs sm:text-sm lg:text-base shadow-xl hover:shadow-2xl transition-all duration-300 border-0 rounded-xl transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
              >
                {isSourceLoading ? (
                  <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 animate-spin mr-2" />
                ) : (
                  <Play className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 mr-2" />
                )}
                Execute Source Query
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Execute the SQL query on the source database</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={executeDestinationQuery}
                disabled={isDestinationLoading || !isDestinationConnected}
                className="w-full h-10 sm:h-12 lg:h-16 bg-gradient-to-r from-green-500 via-green-600 to-green-700 hover:from-green-600 hover:via-green-700 hover:to-green-800 text-white font-semibold text-xs sm:text-sm lg:text-base shadow-xl hover:shadow-2xl transition-all duration-300 border-0 rounded-xl transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
              >
                {isDestinationLoading ? (
                  <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 animate-spin mr-2" />
                ) : (
                  <Play className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 mr-2" />
                )}
                Execute Destination Query
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Execute the SQL query on the destination database</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={compareResults}
                disabled={!sourceResults || !destinationResults}
                className="w-full h-10 sm:h-12 lg:h-16 bg-gradient-to-r from-purple-500 via-purple-600 to-pink-600 hover:from-purple-600 hover:via-purple-700 hover:to-pink-700 text-white font-semibold text-xs sm:text-sm lg:text-base shadow-xl hover:shadow-2xl transition-all duration-300 border-0 rounded-xl transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
              >
                <GitCompare className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 mr-2" />
                Compare Results
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Compare the results from both queries and highlight differences</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={clearQueries}
                className="w-full h-10 sm:h-12 lg:h-16 bg-gradient-to-r from-red-500 via-red-600 to-red-700 hover:from-red-600 hover:via-red-700 hover:to-red-800 text-white font-semibold text-xs sm:text-sm lg:text-base shadow-xl hover:shadow-2xl transition-all duration-300 border-0 rounded-xl transform hover:scale-105"
              >
                <Trash2 className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 mr-2" />
                Clear Queries
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Clear all queries and results to start fresh</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        </div>
      </div>

      {/* Status Section */}
      <div className="mt-4 sm:mt-6 lg:mt-8">
        <div className="bg-white/30 backdrop-blur-sm rounded-lg p-3 border border-white/40 shadow-sm">
          <div className="text-center">
            <h3 className="text-xs sm:text-sm font-semibold text-slate-700 mb-2">Application Status</h3>
            <div className="flex items-center justify-center space-x-4 text-xs text-slate-600">
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${isSourceConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span>Source</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${isDestinationConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span>Destination</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${comparison === 'completed' ? 'bg-blue-500' : 'bg-gray-400'}`}></div>
                <span>Comparison</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ControlPanel;
