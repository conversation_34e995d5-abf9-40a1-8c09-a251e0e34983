# Query Comparison Tool

A React-based application for comparing SQL query results between different databases on the same SQL Server instance.

## Features

- Connect to SQL Server instances
- Execute SQL queries on different databases
- Compare query results side by side
- Visual highlighting of differences
- Filter and search results
- Responsive design for desktop, tablet, and mobile

## Prerequisites

- Node.js (v16 or higher)
- SQL Server instance running locally
- Access to SQL Server with appropriate permissions

## SQL Server Configuration

This application is configured to connect to:
- **Server**: DESKTOP-1TR3ETK
- **Login**: DESKTOP-1TR3ETK\Sohaib Majeed
- **Password**: CodxLoop (case sensitive)
- **Alias**: SohaibLocalInstance

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd query-comparison-tool
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory with your SQL Server credentials:
```env
# SQL Server Configuration
DB_SERVER=DESKTOP-1TR3ETK
DB_USER=DESKTOP-1TR3ETK\\Sohaib Majeed
DB_PASSWORD=CodxLoop
DB_ALIAS=SohaibLocalInstance

# Server Configuration
PORT=3001
```

## Running the Application

### Development Mode

To run both the frontend and backend simultaneously:
```bash
npm run dev:full
```

This will start:
- Backend server on `http://localhost:3001`
- Frontend development server on `http://localhost:8080`

### Individual Services

To run services separately:

**Backend only:**
```bash
npm run server
```

**Frontend only:**
```bash
npm run dev
```

## Usage

1. **Connect to Server**: Toggle the connection switch after selecting a server and database
2. **Enter SQL Query**: Write your SQL query in the text area
3. **Execute Query**: Click the "Execute Source Query" or "Execute Destination Query" button
4. **Compare Results**: After executing both queries, click "Compare Results" to see differences
5. **Filter Results**: Use the filter options to search within results
6. **Clear**: Use "Clear Queries" to reset everything

## API Endpoints

The backend provides the following endpoints:

- `GET /api/health` - Health check
- `GET /api/test-connection` - Test SQL Server connection
- `GET /api/databases` - Get list of available databases
- `POST /api/execute-query` - Execute SQL query
- `GET /api/server-info` - Get server information

## Technologies Used

- **Frontend**: React, TypeScript, Vite, Tailwind CSS, Radix UI
- **Backend**: Node.js, Express, mssql
- **Database**: Microsoft SQL Server

## Security Notes

- The `.env` file contains sensitive credentials and is excluded from version control
- Always use environment variables for database credentials
- Ensure proper SQL Server permissions are configured
- Consider using Windows Authentication in production environments

## Troubleshooting

### Connection Issues
- Verify SQL Server is running and accessible
- Check Windows Firewall settings
- Ensure SQL Server is configured to accept connections
- Verify login credentials and permissions

### Port Conflicts
- Backend runs on port 3001 by default
- Frontend runs on port 8080 by default
- Modify ports in configuration files if needed

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request
